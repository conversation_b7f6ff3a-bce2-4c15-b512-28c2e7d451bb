import { WebClient } from '@slack/web-api';
import { DateTime } from 'luxon';
import { SLACK_NOTIFIER_BOT_TOKEN, VENDOR_PANEL_URL, isDev } from '@/constants';
import { logger } from '@/clean/lib/logger';
import { IQuotation } from '../models/quotation.model';
import { ICorrectiveMaintenanceOrder } from '../models/corrective-maintenance-order.model';

// Slack channel for fleet quotation notifications
const FLEET_QUOTATIONS_CHANNEL = '#mantenimientos';

export interface QuotationNotificationData {
  quotation: IQuotation;
  order: ICorrectiveMaintenanceOrder;
  vehicleInfo: {
    brand: string;
    model: string;
    year: string;
    carNumber: string;
    plates: string;
    vin: string;
  };
  customerInfo: {
    name: string;
    email: string;
    phone?: string;
  };
  workshopInfo: {
    name: string;
    location?: string;
  };
}

export class SlackQuotationNotificationsService {
  private static slackClient = new WebClient(SLACK_NOTIFIER_BOT_TOKEN);

  private static baseUrl = isDev
    ? 'https://develop.vendor.onecarnow.com'
    : VENDOR_PANEL_URL || 'https://vendor.onecarnow.com';

  /**
   * Send notification when a fleet quotation is submitted for approval
   */
  static async sendFleetQuotationPendingNotification(data: QuotationNotificationData): Promise<void> {
    try {
      if (data.quotation.approvalType !== 'fleet') {
        logger.info('Skipping Slack notification for non-fleet quotation', {
          quotationId: data.quotation._id,
          approvalType: data.quotation.approvalType,
        });
        return;
      }

      const quotationUrl = `${this.baseUrl}/corrective-maintenance/quotations/${data.quotation._id}`;
      const orderUrl = `${this.baseUrl}/corrective-maintenance/orders/${data.order._id}`;

      const submittedTime = DateTime.fromJSDate(data.quotation.submittedAt || data.quotation.createdAt)
        .setLocale('es')
        .toFormat("dd 'de' LLLL 'de' yyyy 'a las' HH:mm 'hrs.'");

      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '💰 Nueva Cotización Fleet Pendiente de Aprobación',
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Cotización:* ${data.quotation.quotationNumber}`,
            },
            {
              type: 'mrkdwn',
              text: `*Monto Total:* $${data.quotation.totalEstimatedCost.toLocaleString('es-MX', { minimumFractionDigits: 2 })} MXN`,
            },
            {
              type: 'mrkdwn',
              text: `*Enviada:* ${submittedTime}`,
            },
            // {
            //   type: 'mrkdwn',
            //   text: `*Válida hasta:* ${DateTime.fromJSDate(data.quotation.validUntil).setLocale('es').toFormat('dd/MM/yyyy')}`,
            // }, // Removed - validUntil field not needed
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Vehículo:* ${data.vehicleInfo.brand} ${data.vehicleInfo.model} ${data.vehicleInfo.year}\n*Número:* ${data.vehicleInfo.carNumber} | *Placas:* ${data.vehicleInfo.plates}\n*VIN:* ${data.vehicleInfo.vin}`,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Cliente:* ${data.customerInfo.name}\n*Email:* ${data.customerInfo.email}${data.customerInfo.phone ? `\n*Teléfono:* ${data.customerInfo.phone}` : ''}`,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Taller:* ${data.workshopInfo.name}${data.workshopInfo.location ? `\n*Ubicación:* ${data.workshopInfo.location}` : ''}`,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Servicios (${data.quotation.services.length}):*\n${data.quotation.services
              .map(
                (service) =>
                  `• ${service.serviceName} - $${service.estimatedCost.toLocaleString('es-MX', { minimumFractionDigits: 2 })}`
              )
              .join('\n')}`,
          },
        },
        {
          type: 'actions',
          elements: [
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: '👀 Ver Cotización',
                emoji: true,
              },
              url: quotationUrl,
              style: 'primary',
            },
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: '🔧 Ver Orden',
                emoji: true,
              },
              url: orderUrl,
            },
          ],
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `⏰ Tiempo transcurrido: ${this.getTimeElapsed(data.quotation.submittedAt || data.quotation.createdAt)} | 🆔 Orden: ${data.order._id}`,
            },
          ],
        },
      ];

      await this.slackClient.chat.postMessage({
        channel: FLEET_QUOTATIONS_CHANNEL,
        text: `Nueva cotización fleet pendiente: ${data.quotation.quotationNumber} - $${data.quotation.totalEstimatedCost.toLocaleString('es-MX')}`,
        blocks,
      });

      logger.info('Fleet quotation Slack notification sent successfully', {
        quotationId: data.quotation._id,
        quotationNumber: data.quotation.quotationNumber,
        amount: data.quotation.totalEstimatedCost,
        channel: FLEET_QUOTATIONS_CHANNEL,
      });
    } catch (error) {
      logger.error('Error sending fleet quotation Slack notification', {
        error,
        quotationId: data.quotation._id,
      });
    }
  }

  /**
   * Send reminder notification for pending fleet quotations
   */
  static async sendFleetQuotationReminderNotification(
    data: QuotationNotificationData,
    reminderType: 'daily' | 'urgent' | 'expired'
  ): Promise<void> {
    try {
      const quotationUrl = `${this.baseUrl}/corrective-maintenance/quotations/${data.quotation._id}`;
      const timeElapsed = this.getTimeElapsed(data.quotation.submittedAt || data.quotation.createdAt);
      // const validUntil = DateTime.fromJSDate(data.quotation.validUntil); // Removed - validUntil field not needed
      const now = DateTime.now();
      // const isExpired = now > validUntil; // Removed - validUntil field not needed
      // const daysUntilExpiry = Math.ceil(validUntil.diff(now, 'days').days); // Removed - validUntil field not needed

      let headerText = '';
      let urgencyEmoji = '';
      let style: 'primary' | 'danger' | undefined = 'primary';

      switch (reminderType) {
        case 'daily':
          headerText = '⏰ Recordatorio: Cotización Fleet Pendiente';
          urgencyEmoji = '⏰';
          break;
        case 'urgent':
          headerText = '🚨 URGENTE: Cotización Fleet por Vencer';
          urgencyEmoji = '🚨';
          style = 'danger';
          break;
        case 'expired':
          headerText = '❌ EXPIRADA: Cotización Fleet Vencida';
          urgencyEmoji = '❌';
          style = 'danger';
          break;
      }

      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: headerText,
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${urgencyEmoji} *Cotización:* ${data.quotation.quotationNumber}\n*Monto:* $${data.quotation.totalEstimatedCost.toLocaleString('es-MX', { minimumFractionDigits: 2 })} MXN\n*Tiempo pendiente:* ${timeElapsed}`, // Removed expiry info
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Vehículo:* ${data.vehicleInfo.brand} ${data.vehicleInfo.model} ${data.vehicleInfo.year} (${data.vehicleInfo.carNumber})\n*Cliente:* ${data.customerInfo.name}\n*Taller:* ${data.workshopInfo.name}`,
          },
        },
        {
          type: 'actions',
          elements: [
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: '✅ Aprobar Ahora', // Removed expiry logic
                emoji: true,
              },
              url: quotationUrl,
              style,
            },
          ],
        },
      ];

      await this.slackClient.chat.postMessage({
        channel: FLEET_QUOTATIONS_CHANNEL,
        text: `${headerText}: ${data.quotation.quotationNumber}`,
        blocks,
      });

      logger.info('Fleet quotation reminder Slack notification sent', {
        quotationId: data.quotation._id,
        reminderType,
        timeElapsed,
        // isExpired, // Removed - validUntil field not needed
      });
    } catch (error) {
      logger.error('Error sending fleet quotation reminder notification', {
        error,
        quotationId: data.quotation._id,
        reminderType,
      });
    }
  }

  /**
   * Send notification when fleet quotation is approved or rejected
   */
  static async sendFleetQuotationDecisionNotification(
    data: QuotationNotificationData,
    decision: 'approved' | 'rejected',
    rejectionReason?: string
  ): Promise<void> {
    try {
      const isApproved = decision === 'approved';
      const emoji = isApproved ? '✅' : '❌';
      const action = isApproved ? 'Aprobada' : 'Rechazada';
      const color = isApproved ? 'good' : 'danger';

      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `${emoji} Cotización Fleet ${action}`,
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Cotización:* ${data.quotation.quotationNumber}\n*Monto:* $${data.quotation.totalEstimatedCost.toLocaleString('es-MX', { minimumFractionDigits: 2 })} MXN\n*Vehículo:* ${data.vehicleInfo.brand} ${data.vehicleInfo.model} ${data.vehicleInfo.year} (${data.vehicleInfo.carNumber})`,
          },
        },
      ];

      if (!isApproved && rejectionReason) {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Motivo del rechazo:* ${rejectionReason}`,
          },
        });
      }

      await this.slackClient.chat.postMessage({
        channel: FLEET_QUOTATIONS_CHANNEL,
        text: `Cotización fleet ${action.toLowerCase()}: ${data.quotation.quotationNumber}`,
        blocks,
        attachments: [
          {
            color,
            text: isApproved
              ? 'El trabajo puede proceder según lo cotizado.'
              : 'Se requiere una nueva cotización o ajustes.',
          },
        ],
      });

      logger.info('Fleet quotation decision Slack notification sent', {
        quotationId: data.quotation._id,
        decision,
        rejectionReason,
      });
    } catch (error) {
      logger.error('Error sending fleet quotation decision notification', {
        error,
        quotationId: data.quotation._id,
        decision,
      });
    }
  }

  /**
   * Calculate time elapsed since a given date
   */
  private static getTimeElapsed(date: Date): string {
    const elapsed = DateTime.now().diff(DateTime.fromJSDate(date));
    const hours = Math.floor(elapsed.as('hours'));
    const minutes = Math.floor(elapsed.as('minutes')) % 60;

    if (hours >= 24) {
      const days = Math.floor(hours / 24);
      const remainingHours = hours % 24;
      return `${days} día(s)${remainingHours > 0 ? ` y ${remainingHours} hora(s)` : ''}`;
    } else if (hours > 0) {
      return `${hours} hora(s)${minutes > 0 ? ` y ${minutes} minuto(s)` : ''}`;
    } else {
      return `${minutes} minuto(s)`;
    }
  }

  /**
   * Get pending quotations that need reminders
   */
  static async getPendingQuotationsForReminders(): Promise<{
    daily: QuotationNotificationData[];
    urgent: QuotationNotificationData[];
    expired: QuotationNotificationData[];
  }> {
    try {
      // This would typically query the database for pending quotations
      // and categorize them based on time elapsed and expiry dates
      // For now, returning empty arrays as placeholder
      return {
        daily: [],
        urgent: [],
        expired: [],
      };
    } catch (error) {
      logger.error('Error getting pending quotations for reminders', { error });
      return {
        daily: [],
        urgent: [],
        expired: [],
      };
    }
  }
}
